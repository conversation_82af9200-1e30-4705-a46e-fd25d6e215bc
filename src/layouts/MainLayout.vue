<template>
  <div class="main-layout">
    <!-- 左侧导航栏 -->
    <aside class="sidebar">
      <SidebarNav />
    </aside>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 顶部栏 -->
      <header class="header">
        <TopBar />
      </header>
      
      <!-- 内容区域 -->
      <main class="content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import SidebarNav from '@/components/SidebarNav.vue'
import TopBar from '@/components/TopBar.vue'
</script>

<style scoped>
.main-layout {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.sidebar {
  width: 88px;
  background: linear-gradient(180deg, #4a90e2 0%, #357abd 100%);
  flex-shrink: 0;
  z-index: 1000;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 88px;
  background: #ffffff;
  border-bottom: 1px solid #e6e6e6;
  flex-shrink: 0;
  z-index: 999;
}

.content {
  flex: 1;
  background: #f5f7fa;
  padding: 30px;
  overflow-y: auto;
}
</style>
