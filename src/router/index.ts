import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '@/layouts/MainLayout.vue'
import Dashboard from '@/views/Dashboard.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: MainLayout,
      children: [
        {
          path: '',
          redirect: '/dashboard',
        },
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: Dashboard,
          meta: { title: '工作台' },
        },
        {
          path: 'documents',
          name: 'Documents',
          component: () => import('@/views/Documents.vue'),
          meta: { title: '文档管理' },
        },
        {
          path: 'data-analysis',
          name: 'DataAnalysis',
          component: () => import('@/views/DataAnalysis.vue'),
          meta: { title: '数据分析' },
        },
        {
          path: 'users',
          name: 'Users',
          component: () => import('@/views/Users.vue'),
          meta: { title: '人员管理' },
        },
        {
          path: 'financial',
          name: 'Financial',
          component: () => import('@/views/Financial.vue'),
          meta: { title: '财务管理' },
        },
        {
          path: 'orders',
          name: 'Orders',
          component: () => import('@/views/Orders.vue'),
          meta: { title: '订单管理' },
        },
        {
          path: 'schedule',
          name: 'Schedule',
          component: () => import('@/views/Schedule.vue'),
          meta: { title: '日程管理' },
        },
        {
          path: 'communication',
          name: 'Communication',
          component: () => import('@/views/Communication.vue'),
          meta: { title: '沟通协作' },
        },
        {
          path: 'notifications',
          name: 'Notifications',
          component: () => import('@/views/Notifications.vue'),
          meta: { title: '通知中心' },
        },
        {
          path: 'monitor',
          name: 'Monitor',
          component: () => import('@/views/Monitor.vue'),
          meta: { title: '系统监控' },
        },
        {
          path: 'files',
          name: 'Files',
          component: () => import('@/views/Files.vue'),
          meta: { title: '文件管理' },
        },
        {
          path: 'management',
          name: 'Management',
          component: () => import('@/views/Management.vue'),
          meta: { title: '系统管理' },
        },
        {
          path: 'tools',
          name: 'Tools',
          component: () => import('@/views/Tools.vue'),
          meta: { title: '工具箱' },
        },
        {
          path: 'settings',
          name: 'Settings',
          component: () => import('@/views/Settings.vue'),
          meta: { title: '系统设置' },
        },
        {
          path: 'profile',
          name: 'Profile',
          component: () => import('@/views/Profile.vue'),
          meta: { title: '个人中心' },
        },
      ],
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: { title: '登录' },
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue'),
      meta: { title: '页面未找到' },
    },
  ],
})

export default router
