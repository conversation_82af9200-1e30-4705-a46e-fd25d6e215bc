<template>
  <div 
    class="module-card" 
    :class="{ 'has-badge': badge }"
    @click="handleClick"
  >
    <!-- 徽章 -->
    <div v-if="badge" class="badge">{{ badge }}</div>
    
    <!-- 图标 -->
    <div class="icon-wrapper">
      <el-icon :size="iconSize" :color="iconColor">
        <component :is="icon" />
      </el-icon>
    </div>
    
    <!-- 标题 -->
    <div class="title">{{ title }}</div>
    
    <!-- 描述 -->
    <div v-if="description" class="description">{{ description }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  title: string
  icon: any
  description?: string
  badge?: string | number
  iconSize?: number
  iconColor?: string
  route?: string
}

const props = withDefaults(defineProps<Props>(), {
  iconSize: 48,
  iconColor: '#4a90e2',
  description: '',
  badge: ''
})

const emit = defineEmits<{
  click: [route?: string]
}>()

const handleClick = () => {
  emit('click', props.route)
}
</script>

<style scoped>
.module-card {
  position: relative;
  background: #ffffff;
  border-radius: 12px;
  padding: 24px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e6e8eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  min-height: 140px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #4a90e2;
}

.module-card.has-badge {
  overflow: visible;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4757;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
  margin: 0;
}

.description {
  font-size: 12px;
  color: #7f8c8d;
  line-height: 1.4;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .module-card {
    padding: 20px 16px;
    min-height: 120px;
  }
  
  .title {
    font-size: 14px;
  }
  
  .description {
    font-size: 11px;
  }
}
</style>
