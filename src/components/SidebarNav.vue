<template>
  <div class="sidebar-nav">
    <!-- Logo区域 -->
    <div class="logo-section">
      <div class="logo">
        <el-icon size="32" color="#ffffff">
          <House />
        </el-icon>
      </div>
    </div>
    
    <!-- 导航菜单 -->
    <nav class="nav-menu">
      <div 
        v-for="item in menuItems" 
        :key="item.id"
        class="nav-item"
        :class="{ active: activeMenu === item.id }"
        @click="handleMenuClick(item)"
      >
        <el-icon size="24" color="#ffffff">
          <component :is="item.icon" />
        </el-icon>
        <div class="nav-tooltip">{{ item.name }}</div>
      </div>
    </nav>
    
    <!-- 底部用户区域 -->
    <div class="user-section">
      <div class="nav-item">
        <el-icon size="24" color="#ffffff">
          <User />
        </el-icon>
        <div class="nav-tooltip">用户中心</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  House, 
  User, 
  Document, 
  Setting, 
  DataAnalysis, 
  Monitor,
  Files,
  Management
} from '@element-plus/icons-vue'

const router = useRouter()
const activeMenu = ref('dashboard')

interface MenuItem {
  id: string
  name: string
  icon: any
  route?: string
}

const menuItems: MenuItem[] = [
  { id: 'dashboard', name: '工作台', icon: House, route: '/dashboard' },
  { id: 'documents', name: '文档管理', icon: Document, route: '/documents' },
  { id: 'data', name: '数据分析', icon: DataAnalysis, route: '/data' },
  { id: 'monitor', name: '系统监控', icon: Monitor, route: '/monitor' },
  { id: 'files', name: '文件管理', icon: Files, route: '/files' },
  { id: 'management', name: '系统管理', icon: Management, route: '/management' },
  { id: 'settings', name: '设置', icon: Setting, route: '/settings' }
]

const handleMenuClick = (item: MenuItem) => {
  activeMenu.value = item.id
  if (item.route) {
    router.push(item.route)
  }
}
</script>

<style scoped>
.sidebar-nav {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.logo-section {
  height: 88px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  cursor: pointer;
  transition: transform 0.2s;
}

.logo:hover {
  transform: scale(1.1);
}

.nav-menu {
  flex: 1;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nav-item {
  position: relative;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  margin: 0 8px;
  border-radius: 8px;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-tooltip {
  position: absolute;
  left: 100%;
  margin-left: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  z-index: 1000;
}

.nav-item:hover .nav-tooltip {
  opacity: 1;
  visibility: visible;
}

.user-section {
  padding: 20px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
</style>
