<template>
  <div class="top-bar">
    <!-- 左侧区域 -->
    <div class="left-section">
      <h1 class="system-title">一体化运营平台</h1>
    </div>

    <!-- 中间搜索区域 -->
    <div class="center-section">
      <el-input v-model="searchText" placeholder="搜索功能模块..." class="search-input" clearable>
        <template #prefix>
          <el-icon>
            <Search />
          </el-icon>
        </template>
      </el-input>
    </div>

    <!-- 右侧用户区域 -->
    <div class="right-section">
      <!-- 通知 -->
      <div class="action-item">
        <el-badge :value="notificationCount" :hidden="notificationCount === 0">
          <el-icon size="20">
            <Bell />
          </el-icon>
        </el-badge>
      </div>

      <!-- 消息 -->
      <div class="action-item">
        <el-badge :value="messageCount" :hidden="messageCount === 0">
          <el-icon size="20">
            <Message />
          </el-icon>
        </el-badge>
      </div>

      <!-- 用户信息 -->
      <el-dropdown @command="handleUserCommand">
        <div class="user-info">
          <el-avatar :size="36" :src="userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ username }}</span>
          <el-icon class="dropdown-icon">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">个人中心</el-dropdown-item>
            <el-dropdown-item command="settings">系统设置</el-dropdown-item>
            <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Search, Bell, Message, User, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 响应式数据
const searchText = ref('')
const username = ref('管理员')
const userAvatar = ref('')
const notificationCount = ref(3)
const messageCount = ref(5)

// 用户下拉菜单处理
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      ElMessage.success('退出登录成功')
      // 这里可以添加登出逻辑
      router.push('/login')
      break
  }
}
</script>

<style scoped>
.top-bar {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 30px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.left-section {
  flex: 0 0 auto;
}

.system-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.center-section {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 0 40px;
}

.search-input {
  width: 400px;
  max-width: 100%;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 20px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s;
}

.search-input :deep(.el-input__wrapper:hover) {
  border-color: #4a90e2;
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

.right-section {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 20px;
}

.action-item {
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
  color: #666;
}

.action-item:hover {
  background: #f5f7fa;
  color: #4a90e2;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.dropdown-icon {
  color: #999;
  font-size: 12px;
  transition: transform 0.3s;
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
}
</style>
