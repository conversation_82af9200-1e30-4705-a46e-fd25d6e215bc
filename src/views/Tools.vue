<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">工具箱</h1>
      <p class="page-description">常用工具集合</p>
    </div>

    <div class="page-content">
      <el-card class="content-card">
        <div class="coming-soon">
          <el-icon size="64" color="#4a90e2">
            <Tools />
          </el-icon>
          <h2>功能开发中</h2>
          <p>工具箱功能正在开发中，敬请期待...</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Tools } from '@element-plus/icons-vue'
</script>

<style scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
}

.page-content {
  flex: 1;
}

.content-card {
  height: 100%;
  min-height: 400px;
}

.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  gap: 16px;
}

.coming-soon h2 {
  font-size: 24px;
  color: #2c3e50;
  margin: 0;
}

.coming-soon p {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
}
</style>