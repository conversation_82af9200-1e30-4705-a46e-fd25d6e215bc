<template>
  <div class="dashboard">
    <!-- 主要内容区域 - 四象限布局 -->
    <div class="main-grid">
      <!-- 左上角：近期动态 -->
      <div class="grid-item recent-activities">
        <div class="section-header">
          <h3 class="section-title">
            <el-icon><Bell /></el-icon>
            近期动态
          </h3>
          <el-button text type="primary" size="small">更多</el-button>
        </div>
        <div class="activity-list">
          <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
            <div class="activity-icon">
              <el-icon :color="activity.color">
                <component :is="activity.icon" />
              </el-icon>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右上角：待办/督办事项 -->
      <div class="grid-item todo-items">
        <div class="section-header">
          <h3 class="section-title">
            <el-icon><List /></el-icon>
            待办/督办事项
          </h3>
          <el-badge :value="todoCount" class="todo-badge">
            <el-button text type="primary" size="small">查看全部</el-button>
          </el-badge>
        </div>
        <div class="todo-list">
          <div
            v-for="todo in todoItems"
            :key="todo.id"
            class="todo-item"
            :class="{ urgent: todo.urgent }"
          >
            <div class="todo-priority">
              <el-tag :type="todo.urgent ? 'danger' : 'warning'" size="small">
                {{ todo.urgent ? '督办' : '待办' }}
              </el-tag>
            </div>
            <div class="todo-content">
              <div class="todo-title">{{ todo.title }}</div>
              <div class="todo-deadline">截止：{{ todo.deadline }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 左下角：活动场景 -->
      <div class="grid-item activity-scenes">
        <div class="section-header">
          <h3 class="section-title">
            <el-icon><Location /></el-icon>
            活动场景
          </h3>
          <el-button text type="primary" size="small">管理</el-button>
        </div>
        <div class="scenes-grid">
          <div
            v-for="scene in activityScenes"
            :key="scene.id"
            class="scene-card"
            @click="handleSceneClick(scene)"
          >
            <div class="scene-icon">
              <el-icon :size="32" :color="scene.color">
                <component :is="scene.icon" />
              </el-icon>
            </div>
            <div class="scene-name">{{ scene.name }}</div>
            <div class="scene-count">{{ scene.count }}</div>
          </div>
        </div>
      </div>

      <!-- 右下角：常用流程 -->
      <div class="grid-item common-processes">
        <div class="section-header">
          <h3 class="section-title">
            <el-icon><Operation /></el-icon>
            常用流程
          </h3>
          <el-button text type="primary" size="small">自定义</el-button>
        </div>
        <div class="processes-grid">
          <div
            v-for="process in commonProcesses"
            :key="process.id"
            class="process-card"
            @click="handleProcessClick(process)"
          >
            <div class="process-icon">
              <el-icon :size="28" :color="process.color">
                <component :is="process.icon" />
              </el-icon>
            </div>
            <div class="process-name">{{ process.name }}</div>
            <div class="process-badge" v-if="process.badge">
              <el-badge :value="process.badge" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  Bell,
  List,
  Location,
  Operation,
  Document,
  User,
  Setting,
  Monitor,
  Files,
  Management,
  Wallet,
  ShoppingCart,
  Calendar,
  ChatDotRound,
  Tools,
  Edit,
  Check,
  Warning,
  OfficeBuilding,
  School,
  Shop,
  House,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 近期动态数据
interface Activity {
  id: string
  title: string
  time: string
  icon: any
  color: string
}

const recentActivities = ref<Activity[]>([
  {
    id: '1',
    title: '系统升级维护通知已发布',
    time: '2小时前',
    icon: Bell,
    color: '#4a90e2',
  },
  {
    id: '2',
    title: '月度财务报表已生成',
    time: '4小时前',
    icon: Document,
    color: '#67c23a',
  },
  {
    id: '3',
    title: '新员工入职流程已完成',
    time: '6小时前',
    icon: User,
    color: '#e6a23c',
  },
  {
    id: '4',
    title: '设备采购申请已审批',
    time: '1天前',
    icon: Check,
    color: '#67c23a',
  },
  {
    id: '5',
    title: '安全检查报告已提交',
    time: '2天前',
    icon: Monitor,
    color: '#f56c6c',
  },
])

// 待办/督办事项数据
interface TodoItem {
  id: string
  title: string
  deadline: string
  urgent: boolean
}

const todoCount = ref(8)
const todoItems = ref<TodoItem[]>([
  {
    id: '1',
    title: '季度预算审核',
    deadline: '今天 18:00',
    urgent: true,
  },
  {
    id: '2',
    title: '员工绩效评估',
    deadline: '明天 12:00',
    urgent: false,
  },
  {
    id: '3',
    title: '供应商合同续签',
    deadline: '2024-01-15',
    urgent: true,
  },
  {
    id: '4',
    title: '设备维护计划制定',
    deadline: '2024-01-18',
    urgent: false,
  },
  {
    id: '5',
    title: '安全培训安排',
    deadline: '2024-01-20',
    urgent: false,
  },
])

// 活动场景数据
interface Scene {
  id: string
  name: string
  count: string
  icon: any
  color: string
}

const activityScenes = ref<Scene[]>([
  {
    id: '1',
    name: '会议室',
    count: '5个',
    icon: OfficeBuilding,
    color: '#4a90e2',
  },
  {
    id: '2',
    name: '培训室',
    count: '3个',
    icon: School,
    color: '#67c23a',
  },
  {
    id: '3',
    name: '展厅',
    count: '2个',
    icon: Shop,
    color: '#e6a23c',
  },
  {
    id: '4',
    name: '休息区',
    count: '8个',
    icon: House,
    color: '#f56c6c',
  },
])

// 常用流程数据
interface Process {
  id: string
  name: string
  icon: any
  color: string
  badge?: number
}

const commonProcesses = ref<Process[]>([
  {
    id: '1',
    name: '请假申请',
    icon: Calendar,
    color: '#4a90e2',
    badge: 3,
  },
  {
    id: '2',
    name: '报销流程',
    icon: Wallet,
    color: '#67c23a',
  },
  {
    id: '3',
    name: '采购申请',
    icon: ShoppingCart,
    color: '#e6a23c',
    badge: 1,
  },
  {
    id: '4',
    name: '文档审批',
    icon: Document,
    color: '#f56c6c',
  },
  {
    id: '5',
    name: '设备维修',
    icon: Tools,
    color: '#909399',
  },
  {
    id: '6',
    name: '人员调动',
    icon: User,
    color: '#409eff',
  },
])

// 事件处理方法
const handleSceneClick = (scene: Scene) => {
  ElMessage.info(`点击了活动场景：${scene.name}`)
}

const handleProcessClick = (process: Process) => {
  ElMessage.info(`启动流程：${process.name}`)
}
</script>

<style scoped>
.dashboard {
  height: 100%;
  padding: 0;
}

.main-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  height: 100%;
  min-height: 600px;
}

.grid-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e6e8eb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f2f5;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

/* 近期动态样式 */
.activity-list {
  flex: 1;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f5f7fa;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-size: 14px;
  color: #2c3e50;
  margin-bottom: 4px;
  line-height: 1.4;
}

.activity-time {
  font-size: 12px;
  color: #7f8c8d;
}

/* 待办事项样式 */
.todo-badge {
  margin-left: 8px;
}

.todo-list {
  flex: 1;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f5f7fa;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-item.urgent {
  background: #fef0f0;
  margin: 0 -12px;
  padding: 12px;
  border-radius: 6px;
}

.todo-priority {
  flex-shrink: 0;
}

.todo-content {
  flex: 1;
  min-width: 0;
}

.todo-title {
  font-size: 14px;
  color: #2c3e50;
  margin-bottom: 4px;
  line-height: 1.4;
}

.todo-deadline {
  font-size: 12px;
  color: #7f8c8d;
}

/* 活动场景样式 */
.scenes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  flex: 1;
}

.scene-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  gap: 8px;
}

.scene-card:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.scene-icon {
  margin-bottom: 8px;
}

.scene-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.scene-count {
  font-size: 12px;
  color: #7f8c8d;
}

/* 常用流程样式 */
.processes-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  flex: 1;
}

.process-card {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px 8px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  gap: 8px;
}

.process-card:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.process-icon {
  margin-bottom: 4px;
}

.process-name {
  font-size: 12px;
  font-weight: 500;
  color: #2c3e50;
  line-height: 1.2;
}

.process-badge {
  position: absolute;
  top: -4px;
  right: -4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, auto);
    gap: 16px;
    min-height: auto;
  }

  .grid-item {
    min-height: 300px;
  }

  .scenes-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
  }

  .processes-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .scene-card,
  .process-card {
    padding: 12px 8px;
  }
}

@media (max-width: 480px) {
  .main-grid {
    gap: 12px;
  }

  .grid-item {
    padding: 16px;
    min-height: 250px;
  }

  .section-title {
    font-size: 14px;
  }

  .scenes-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .processes-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
