<template>
  <div class="dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <h2 class="welcome-title">欢迎使用企业运营管理平台</h2>
      <p class="welcome-subtitle">选择下方功能模块开始您的工作</p>
    </div>
    
    <!-- 功能模块区域 -->
    <div class="modules-section">
      <!-- 常用功能 -->
      <div class="section-group">
        <h3 class="section-title">常用功能</h3>
        <div class="modules-grid">
          <ModuleCard
            v-for="module in commonModules"
            :key="module.id"
            :title="module.title"
            :icon="module.icon"
            :description="module.description"
            :badge="module.badge"
            :route="module.route"
            @click="handleModuleClick"
          />
        </div>
      </div>
      
      <!-- 业务管理 -->
      <div class="section-group">
        <h3 class="section-title">业务管理</h3>
        <div class="modules-grid">
          <ModuleCard
            v-for="module in businessModules"
            :key="module.id"
            :title="module.title"
            :icon="module.icon"
            :description="module.description"
            :badge="module.badge"
            :route="module.route"
            @click="handleModuleClick"
          />
        </div>
      </div>
      
      <!-- 系统工具 -->
      <div class="section-group">
        <h3 class="section-title">系统工具</h3>
        <div class="modules-grid">
          <ModuleCard
            v-for="module in systemModules"
            :key="module.id"
            :title="module.title"
            :icon="module.icon"
            :description="module.description"
            :badge="module.badge"
            :route="module.route"
            @click="handleModuleClick"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import ModuleCard from '@/components/ModuleCard.vue'
import {
  Document,
  DataAnalysis,
  User,
  Setting,
  Monitor,
  Files,
  Management,
  Wallet,
  ShoppingCart,
  Calendar,
  ChatDotRound,
  Bell,
  Tools
} from '@element-plus/icons-vue'

const router = useRouter()

interface Module {
  id: string
  title: string
  icon: any
  description: string
  badge?: string | number
  route?: string
}

// 常用功能模块
const commonModules: Module[] = [
  {
    id: 'documents',
    title: '文档管理',
    icon: Document,
    description: '企业文档存储与管理',
    badge: '新',
    route: '/documents'
  },
  {
    id: 'data-analysis',
    title: '数据分析',
    icon: DataAnalysis,
    description: '业务数据统计分析',
    route: '/data-analysis'
  },
  {
    id: 'user-management',
    title: '人员管理',
    icon: User,
    description: '员工信息管理',
    route: '/users'
  },
  {
    id: 'financial',
    title: '财务管理',
    icon: Wallet,
    description: '财务数据管理',
    badge: 5,
    route: '/financial'
  }
]

// 业务管理模块
const businessModules: Module[] = [
  {
    id: 'orders',
    title: '订单管理',
    icon: ShoppingCart,
    description: '订单处理与跟踪',
    badge: 12,
    route: '/orders'
  },
  {
    id: 'schedule',
    title: '日程管理',
    icon: Calendar,
    description: '工作日程安排',
    route: '/schedule'
  },
  {
    id: 'communication',
    title: '沟通协作',
    icon: ChatDotRound,
    description: '团队沟通工具',
    badge: 3,
    route: '/communication'
  },
  {
    id: 'notifications',
    title: '通知中心',
    icon: Bell,
    description: '系统通知管理',
    route: '/notifications'
  }
]

// 系统工具模块
const systemModules: Module[] = [
  {
    id: 'system-monitor',
    title: '系统监控',
    icon: Monitor,
    description: '系统运行状态监控',
    route: '/monitor'
  },
  {
    id: 'file-management',
    title: '文件管理',
    icon: Files,
    description: '系统文件管理',
    route: '/files'
  },
  {
    id: 'system-management',
    title: '系统管理',
    icon: Management,
    description: '系统配置管理',
    route: '/management'
  },
  {
    id: 'tools',
    title: '工具箱',
    icon: Tools,
    description: '常用工具集合',
    route: '/tools'
  },
  {
    id: 'settings',
    title: '系统设置',
    icon: Setting,
    description: '系统参数设置',
    route: '/settings'
  }
]

const handleModuleClick = (route?: string) => {
  if (route) {
    router.push(route)
  }
}
</script>

<style scoped>
.dashboard {
  max-width: 1400px;
  margin: 0 auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
}

.welcome-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px 0;
}

.welcome-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.modules-section {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.section-group {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 24px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid #f1f3f4;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .modules-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .welcome-title {
    font-size: 24px;
  }
  
  .welcome-subtitle {
    font-size: 14px;
  }
  
  .section-group {
    padding: 20px;
  }
  
  .modules-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .modules-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
